"""Admin dashboard views and analytics."""

# --- Standard Library Imports ---
import csv
from datetime import date, timedelta
from decimal import Decimal

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Avg, Count, Q, Sum
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.utils import timezone
from django.views import View
from django.views.decorators.http import require_http_methods
from django.views.generic import DetailView, ListView


try:
    import psutil  # noqa: F401
    PSUTIL_AVAILABLE = True
except ImportError:  # pragma: no cover - optional dependency
    PSUTIL_AVAILABLE = False

# --- Local App Imports ---
from accounts_app.models import (
    CustomUser,
    CustomerProfile,
    ServiceProviderProfile,
    TeamMember,
)
from booking_cart_app.models import Booking, BookingItem
from venues_app.models import Service, Venue
from ..forms import DateRangeForm
from ..helpers import get_date_range, get_int_param, get_valid_param
from ..logging_utils import (
    log_admin_dashboard_access,
    log_admin_data_access,
    log_analytics_access,
    log_booking_status_access,
    log_dashboard_access,
    log_dashboard_activity,
    log_dashboard_filter_usage,
    log_error,
    log_favorite_venue_event,
    log_profile_access,
    log_provider_dashboard_activity,
    log_provider_earnings_access,
    log_provider_service_stats_access,
    log_security_event,
    log_system_health_check,
    performance_monitor,
)
from ..decorators import customer_required, provider_required, staff_required
from ..mixins import ProviderRequiredMixin
from ..models import FavoriteVenue
from ..constants import (
    CUSTOMER_ONLY_ERROR,
    PROVIDER_ONLY_ERROR,
    ADMIN_ONLY_ERROR,
    FAVORITE_ADDED_SUCCESS,
    FAVORITE_REMOVED_SUCCESS,
    FAVORITE_ALREADY_EXISTS,
    DASHBOARD_ACCESS_SUCCESS,
    VENUE_NOT_FOUND_ERROR,
    PERMISSION_DENIED_ERROR,
)

# Try to import optional models
try:
    from review_app.models import Review
except ImportError:  # pragma: no cover - optional dependency
    Review = None

try:
    from payments_app.models import Payment, RefundRequest
except ImportError:  # pragma: no cover - optional dependency
    Payment = None
    RefundRequest = None


# --- Admin Dashboard Views ---

@staff_required
@performance_monitor("admin_dashboard_access")
def admin_dashboard(request):
    """
    Main admin dashboard view showing platform overview and key metrics.
    """
    # Role check handled by staff_required decorator

    try:
        cache_key = "admin_dashboard_stats"
        context = cache.get(cache_key)
        if context:
            return render(request, 'dashboard_app/admin/dashboard.html', context)

        # Get user statistics
        total_users = CustomUser.objects.count()
        customer_count = CustomUser.objects.filter(role=CustomUser.CUSTOMER).count()
        provider_count = CustomUser.objects.filter(role=CustomUser.SERVICE_PROVIDER).count()
        staff_count = CustomUser.objects.filter(is_staff=True).count()

        # Get venue statistics
        total_venues = Venue.objects.count()
        pending_venues = Venue.objects.filter(approval_status='pending').count()
        approved_venues = Venue.objects.filter(approval_status='approved').count()
        rejected_venues = Venue.objects.filter(approval_status='rejected').count()

        # Get booking statistics
        total_bookings = Booking.objects.count()
        pending_bookings = Booking.objects.filter(status='pending').count()
        confirmed_bookings = Booking.objects.filter(status='confirmed').count()
        cancelled_bookings = Booking.objects.filter(status='cancelled').count()
        completed_bookings = Booking.objects.filter(status='completed').count()

        # Get revenue statistics
        total_revenue = Booking.objects.filter(
            status__in=['confirmed', 'completed']
        ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')

        # Get recent bookings
        recent_bookings = (
            Booking.objects.select_related('customer', 'venue')
            .order_by('-booking_date')[:10]
        )

        # Get recent reviews if available
        recent_reviews = []
        if Review:
            recent_reviews = (
                Review.objects.select_related('customer', 'venue')
                .order_by('-created_at')[:10]
            )

        # Log admin dashboard access with comprehensive metrics
        log_admin_dashboard_access(
            user=request.user,
            admin_section='main_dashboard',
            request=request,
            details={
                'total_users': total_users,
                'customer_count': customer_count,
                'provider_count': provider_count,
                'staff_count': staff_count,
                'total_venues': total_venues,
                'pending_venues': pending_venues,
                'approved_venues': approved_venues,
                'rejected_venues': rejected_venues,
                'total_bookings': total_bookings,
                'pending_bookings': pending_bookings,
                'confirmed_bookings': confirmed_bookings,
                'cancelled_bookings': cancelled_bookings,
                'completed_bookings': completed_bookings,
                'total_revenue': str(total_revenue)
            }
        )

        context = {
            'total_users': total_users,
            'customer_count': customer_count,
            'provider_count': provider_count,
            'staff_count': staff_count,
            'total_venues': total_venues,
            'pending_venues': pending_venues,
            'approved_venues': approved_venues,
            'rejected_venues': rejected_venues,
            'total_bookings': total_bookings,
            'pending_bookings': pending_bookings,
            'confirmed_bookings': confirmed_bookings,
            'cancelled_bookings': cancelled_bookings,
            'completed_bookings': completed_bookings,
            'total_revenue': total_revenue,
            'recent_bookings': recent_bookings,
            'recent_reviews': recent_reviews,
        }

        cache.set(cache_key, context, settings.DASHBOARD_CACHE_TIMEOUT)

        return render(request, 'dashboard_app/admin/dashboard.html', context)

    except Exception as e:
        log_error(
            error_type='admin_dashboard_access',
            error_message="Error loading admin dashboard",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, 'There was an error loading the admin dashboard. Please try again.')
        return redirect('utility_app:home')


@staff_required
@performance_monitor("admin_platform_overview_access")
def admin_platform_overview(request):
    """
    Admin platform overview with detailed analytics and date range filtering.
    """
    # Role check handled by staff_required decorator

    try:
        # Get date range form
        form = DateRangeForm(request.GET or None)
        start_date, end_date = get_date_range(form)

        # Get user statistics
        total_users = CustomUser.objects.count()
        new_users = CustomUser.objects.filter(
            date_joined__date__gte=start_date,
            date_joined__date__lte=end_date
        ).count()
        customer_count = CustomUser.objects.filter(role=CustomUser.CUSTOMER).count()
        provider_count = CustomUser.objects.filter(role=CustomUser.SERVICE_PROVIDER).count()

        # Get venue statistics
        total_venues = Venue.objects.count()
        new_venues = Venue.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        ).count()
        pending_venues = Venue.objects.filter(approval_status='pending').count()
        approved_venues = Venue.objects.filter(approval_status='approved').count()

        # Get booking statistics
        total_bookings = Booking.objects.count()
        new_bookings = Booking.objects.filter(
            booking_date__date__gte=start_date,
            booking_date__date__lte=end_date
        ).count()
        confirmed_bookings = Booking.objects.filter(status='confirmed').count()
        cancelled_bookings = Booking.objects.filter(status='cancelled').count()

        # Get revenue statistics
        total_revenue = Booking.objects.filter(
            status__in=['confirmed', 'completed']
        ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')

        period_revenue = Booking.objects.filter(
            status__in=['confirmed', 'completed'],
            booking_date__date__gte=start_date,
            booking_date__date__lte=end_date
        ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')

        # Get daily statistics for the period (limited to 30 days for performance)
        chart_start = max(start_date, end_date - timedelta(days=30))
        daily_stats = {}
        current_date = chart_start
        while current_date <= end_date:
            # Get new users for this day
            day_new_users = CustomUser.objects.filter(
                date_joined__date=current_date
            ).count()

            # Get new bookings for this day
            day_new_bookings = Booking.objects.filter(
                booking_date__date=current_date
            ).count()

            # Get revenue for this day
            day_revenue = Booking.objects.filter(
                status__in=['confirmed', 'completed'],
                booking_date__date=current_date
            ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')

            # Store daily stats
            daily_stats[current_date.strftime('%Y-%m-%d')] = {
                'date': current_date,
                'new_users': day_new_users,
                'new_bookings': day_new_bookings,
                'revenue': float(day_revenue),
            }

            current_date += timedelta(days=1)

        # Log platform overview access with analytics context
        log_analytics_access(
            user=request.user,
            analytics_type='platform_overview',
            date_range=f"{start_date} to {end_date}",
            metrics_accessed={
                'total_users': total_users,
                'new_users': new_users,
                'customer_count': customer_count,
                'provider_count': provider_count,
                'total_venues': total_venues,
                'new_venues': new_venues,
                'total_bookings': total_bookings,
                'new_bookings': new_bookings,
                'total_revenue': str(total_revenue),
                'period_revenue': str(period_revenue),
                'daily_stats_count': len(daily_stats)
            },
            request=request
        )

        context = {
            'form': form,
            'start_date': start_date,
            'end_date': end_date,
            'total_users': total_users,
            'new_users': new_users,
            'customer_count': customer_count,
            'provider_count': provider_count,
            'total_venues': total_venues,
            'new_venues': new_venues,
            'pending_venues': pending_venues,
            'approved_venues': approved_venues,
            'total_bookings': total_bookings,
            'new_bookings': new_bookings,
            'confirmed_bookings': confirmed_bookings,
            'cancelled_bookings': cancelled_bookings,
            'total_revenue': total_revenue,
            'period_revenue': period_revenue,
            'daily_stats': daily_stats,
        }

        return render(request, 'dashboard_app/admin/platform_overview.html', context)

    except Exception as e:
        log_error(
            error_type='admin_platform_overview_access',
            error_message="Error loading admin platform overview",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, 'There was an error loading the platform overview. Please try again.')
        return redirect('dashboard_app:admin_dashboard')


@staff_required
@performance_monitor("admin_user_statistics_access")
def admin_user_statistics(request):
    """
    Admin user statistics view with detailed user analytics and activity metrics.
    """
    # Role check handled by staff_required decorator

    try:
        # Get date range form
        form = DateRangeForm(request.GET or None)
        start_date, end_date = get_date_range(form)

        # Get user statistics
        total_users = CustomUser.objects.count()
        customer_count = CustomUser.objects.filter(role=CustomUser.CUSTOMER).count()
        provider_count = CustomUser.objects.filter(role=CustomUser.SERVICE_PROVIDER).count()
        staff_count = CustomUser.objects.filter(is_staff=True).count()

        # Get new users in the period
        new_users_count = CustomUser.objects.filter(
            date_joined__date__gte=start_date,
            date_joined__date__lte=end_date
        ).count()

        new_customers = CustomUser.objects.filter(
            role=CustomUser.CUSTOMER,
            date_joined__date__gte=start_date,
            date_joined__date__lte=end_date
        ).count()

        new_providers = CustomUser.objects.filter(
            role=CustomUser.SERVICE_PROVIDER,
            date_joined__date__gte=start_date,
            date_joined__date__lte=end_date
        ).count()

        # Get active users (users who have logged in within the last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        active_users_count = CustomUser.objects.filter(
            last_login__gte=thirty_days_ago
        ).count()

        # Get daily registrations for the period (limited to 30 days for performance)
        chart_start = max(start_date, end_date - timedelta(days=30))
        daily_registrations = {}
        current_date = chart_start
        while current_date <= end_date:
            day_registrations = CustomUser.objects.filter(
                date_joined__date=current_date
            ).count()
            daily_registrations[current_date.strftime('%Y-%m-%d')] = day_registrations
            current_date += timedelta(days=1)

        # Get top 10 customers (by number of bookings made)
        top_customers = CustomUser.objects.filter(role=CustomUser.CUSTOMER).annotate(
            booking_count=Count('bookings')
        ).order_by('-booking_count')[:10]

        # Get top 10 service providers (by number of bookings received)
        top_providers = CustomUser.objects.filter(role=CustomUser.SERVICE_PROVIDER).annotate(
            venue_count=Count('service_provider_profile__venue'),
            booking_count=Count('service_provider_profile__venue__bookings')
        ).order_by('-booking_count')[:10]

        # Log user statistics access with data access tracking
        log_admin_data_access(
            user=request.user,
            data_type='user_statistics',
            access_type='analytics',
            record_count=total_users,
            filters_applied={
                'date_range': f"{start_date} to {end_date}",
                'period_days': (end_date - start_date).days
            },
            request=request
        )

        # Log analytics access
        log_analytics_access(
            user=request.user,
            analytics_type='user_statistics',
            date_range=f"{start_date} to {end_date}",
            metrics_accessed={
                'total_users': total_users,
                'customer_count': customer_count,
                'provider_count': provider_count,
                'staff_count': staff_count,
                'new_users_count': new_users_count,
                'new_customers': new_customers,
                'new_providers': new_providers,
                'active_users_count': active_users_count,
                'top_customers_count': len(top_customers),
                'top_providers_count': len(top_providers)
            },
            request=request
        )

        context = {
            'form': form,
            'start_date': start_date,
            'end_date': end_date,
            'total_users': total_users,
            'customer_count': customer_count,
            'provider_count': provider_count,
            'staff_count': staff_count,
            'new_users_count': new_users_count,
            'new_customers': new_customers,
            'new_providers': new_providers,
            'active_users_count': active_users_count,
            'daily_registrations': daily_registrations,
            'top_customers': top_customers,
            'top_providers': top_providers,
        }

        return render(request, 'dashboard_app/admin/user_statistics.html', context)

    except Exception as e:
        log_error(
            error_type='admin_user_statistics_access',
            error_message="Error loading admin user statistics",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, 'There was an error loading user statistics. Please try again.')
        return redirect('dashboard_app:admin_dashboard')


@staff_required
@performance_monitor("admin_booking_analytics_redirect")
def admin_booking_analytics(request):
    """
    Admin booking analytics view - redirects to booking_cart_app analytics.
    """
    # Role check handled by staff_required decorator

    # Log booking analytics access
    log_dashboard_activity(
        activity_type='admin_booking_analytics_redirect',
        user=request.user,
        request=request,
        details={'redirect_to': 'booking_cart_app:admin_booking_analytics'}
    )

    # Redirect to the booking_cart_app admin booking analytics view
    return redirect('booking_cart_app:admin_booking_analytics')


@staff_required
@performance_monitor("admin_revenue_tracking_access")
def admin_revenue_tracking(request):
    """
    Admin revenue tracking view with comprehensive revenue analytics.
    """
    # Role check handled by staff_required decorator

    try:
        # Get date range form
        form = DateRangeForm(request.GET or None)
        start_date, end_date = get_date_range(form)

        # Get revenue data for the period
        bookings = Booking.objects.filter(
            status__in=['confirmed', 'completed'],
            booking_date__date__gte=start_date,
            booking_date__date__lte=end_date
        )

        total_revenue = bookings.aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')

        # Get daily revenue for chart
        daily_revenue = {}
        current_date = start_date
        while current_date <= end_date:
            day_revenue = Booking.objects.filter(
                status__in=['confirmed', 'completed'],
                booking_date__date=current_date
            ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')

            daily_revenue[current_date.strftime('%Y-%m-%d')] = float(day_revenue)
            current_date += timedelta(days=1)

        # Get revenue by venue category (if categories exist)
        category_revenue = []
        try:
            from venues_app.models import Category
            categories = Category.objects.all()
            for category in categories:
                cat_revenue = Booking.objects.filter(
                    status__in=['confirmed', 'completed'],
                    venue__categories=category,
                    booking_date__date__gte=start_date,
                    booking_date__date__lte=end_date
                ).aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')

                if cat_revenue > 0:
                    category_revenue.append({
                        'category': category.category_name,
                        'revenue': float(cat_revenue)
                    })
        except ImportError:
            pass

        # Sort categories by revenue
        sorted_categories = sorted(category_revenue, key=lambda x: x['revenue'], reverse=True)

        # Get top performing venues
        top_venues = Venue.objects.annotate(
            revenue=Sum('bookings__total_price', filter=Q(
                bookings__status__in=['confirmed', 'completed'],
                bookings__booking_date__date__gte=start_date,
                bookings__booking_date__date__lte=end_date
            ))
        ).filter(revenue__gt=0).order_by('-revenue')[:10]

        # Get top performing services
        top_services = Service.objects.annotate(
            revenue=Sum('booking_items__service_price', filter=Q(
                booking_items__booking__status__in=['confirmed', 'completed'],
                booking_items__scheduled_date__gte=start_date,
                booking_items__scheduled_date__lte=end_date
            ))
        ).filter(revenue__gt=0).order_by('-revenue')[:10]

        # Log revenue tracking access
        log_dashboard_activity(
            activity_type='admin_revenue_tracking_access',
            user=request.user,
            request=request,
            details={
                'date_range': f"{start_date} to {end_date}",
                'total_revenue': str(total_revenue),
                'bookings_count': bookings.count()
            }
        )

        context = {
            'form': form,
            'start_date': start_date,
            'end_date': end_date,
            'bookings': bookings,
            'total_revenue': total_revenue,
            'daily_revenue': daily_revenue,
            'category_revenue': category_revenue,
            'sorted_categories': sorted_categories,
            'top_venues': top_venues,
            'top_services': top_services,
        }

        return render(request, 'dashboard_app/admin/revenue_tracking.html', context)

    except Exception as e:
        log_error(
            error_type='admin_revenue_tracking_access',
            error_message="Error loading admin revenue tracking",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, 'There was an error loading revenue tracking. Please try again.')
        return redirect('dashboard_app:admin_dashboard')


@staff_required
@performance_monitor("admin_revenue_export")
def admin_revenue_export(request):
    """Export revenue data as CSV for admins."""
    form = DateRangeForm(request.GET or None)
    start_date, end_date = get_date_range(form)

    bookings = Booking.objects.filter(
        status__in=['confirmed', 'completed'],
        booking_date__date__gte=start_date,
        booking_date__date__lte=end_date
    ).select_related('customer', 'venue')

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="revenue_{start_date}_{end_date}.csv"'
    writer = csv.writer(response)
    writer.writerow(['Date', 'Venue', 'Customer', 'Total Price', 'Status'])
    for booking in bookings:
        writer.writerow([
            booking.booking_date,
            booking.venue.venue_name,
            booking.customer.email,
            booking.total_price,
            booking.status,
        ])
    return response


@staff_required
@performance_monitor("admin_system_health_access")
def admin_system_health(request):
    """
    Comprehensive system health monitoring for admins.
    Displays database statistics, performance metrics, error logs, and system status.
    """
    # Role check handled by staff_required decorator

    try:
        # Get database statistics with growth metrics
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)

        # Current counts
        total_users = CustomUser.objects.count()
        total_venues = Venue.objects.count()
        total_services = Service.objects.count()
        total_bookings = Booking.objects.count()
        total_booking_items = BookingItem.objects.count()

        # Growth metrics (last 7 days)
        new_users_week = CustomUser.objects.filter(date_joined__gte=week_ago).count()
        new_venues_week = Venue.objects.filter(created_at__gte=week_ago).count()
        new_bookings_week = Booking.objects.filter(booking_date__gte=week_ago).count()

        # Growth metrics (last 30 days)
        new_users_month = CustomUser.objects.filter(date_joined__gte=month_ago).count()
        new_venues_month = Venue.objects.filter(created_at__gte=month_ago).count()
        new_bookings_month = Booking.objects.filter(booking_date__gte=month_ago).count()

        db_stats = {
            'users': {
                'total': total_users,
                'week_growth': new_users_week,
                'month_growth': new_users_month,
                'week_percentage': round((new_users_week / total_users * 100) if total_users > 0 else 0, 1),
                'month_percentage': round((new_users_month / total_users * 100) if total_users > 0 else 0, 1)
            },
            'venues': {
                'total': total_venues,
                'week_growth': new_venues_week,
                'month_growth': new_venues_month,
                'week_percentage': round((new_venues_week / total_venues * 100) if total_venues > 0 else 0, 1),
                'month_percentage': round((new_venues_month / total_venues * 100) if total_venues > 0 else 0, 1)
            },
            'services': {
                'total': total_services,
                'active': Service.objects.filter(is_active=True).count(),
                'inactive': Service.objects.filter(is_active=False).count()
            },
            'bookings': {
                'total': total_bookings,
                'week_growth': new_bookings_week,
                'month_growth': new_bookings_month,
                'pending': Booking.objects.filter(status='pending').count(),
                'confirmed': Booking.objects.filter(status='confirmed').count(),
                'completed': Booking.objects.filter(status='completed').count(),
                'cancelled': Booking.objects.filter(status='cancelled').count()
            },
            'booking_items': {
                'total': total_booking_items,
                'today': BookingItem.objects.filter(scheduled_date=today).count()
            }
        }

        # Try to get review and transaction counts (may not exist in all setups)
        if Review:
            db_stats['reviews'] = {
                'total': Review.objects.count(),
                'week_growth': Review.objects.filter(created_at__gte=week_ago).count()
            }
        else:
            db_stats['reviews'] = {'total': 0, 'week_growth': 0}

        if Payment and RefundRequest:
            db_stats['payments'] = {
                'total': Payment.objects.count(),
                'week_growth': Payment.objects.filter(created_at__gte=week_ago).count()
            }
            db_stats['refund_requests'] = {
                'total': RefundRequest.objects.count(),
                'week_growth': RefundRequest.objects.filter(created_at__gte=week_ago).count()
            }
        else:
            db_stats['payments'] = {'total': 0, 'week_growth': 0}
            db_stats['refund_requests'] = {'total': 0, 'week_growth': 0}

        # Get system performance metrics (simulated for demo)
        if PSUTIL_AVAILABLE:
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            performance_metrics = {
                'cpu_usage': round(cpu_usage, 1),
                'memory_usage': round(memory.percent, 1),
                'memory_total': round(memory.total / (1024**3), 1),  # GB
                'memory_available': round(memory.available / (1024**3), 1),  # GB
                'disk_usage': round(disk.percent, 1),
                'disk_total': round(disk.total / (1024**3), 1),  # GB
                'disk_free': round(disk.free / (1024**3), 1),  # GB
            }
        else:
            # Fallback if psutil is not available
            performance_metrics = {
                'cpu_usage': 25.0,
                'memory_usage': 40.0,
                'memory_total': 8.0,
                'memory_available': 4.8,
                'disk_usage': 30.0,
                'disk_total': 100.0,
                'disk_free': 70.0,
            }

        # Get recent system events (simulated - in production would come from logs)
        recent_events = [
            {
                'timestamp': timezone.now() - timedelta(minutes=30),
                'level': 'INFO',
                'message': 'System health check completed successfully',
                'source': 'dashboard_app.views',
            },
            {
                'timestamp': timezone.now() - timedelta(hours=2),
                'level': 'WARNING',
                'message': 'High memory usage detected',
                'source': 'system_monitor',
            },
            {
                'timestamp': timezone.now() - timedelta(hours=6),
                'level': 'INFO',
                'message': 'Database backup completed',
                'source': 'backup_service',
            },
        ]

        # Get application health status
        app_health = {
            'database': 'healthy',  # Could check DB connection
            'cache': 'healthy',     # Could check cache connection
            'storage': 'healthy',   # Could check file storage
            'email': 'healthy',     # Could check email service
        }

        # Calculate overall system health score
        health_score = 100
        if performance_metrics['cpu_usage'] > 80:
            health_score -= 20
        elif performance_metrics['cpu_usage'] > 60:
            health_score -= 10

        if performance_metrics['memory_usage'] > 85:
            health_score -= 20
        elif performance_metrics['memory_usage'] > 70:
            health_score -= 10

        if performance_metrics['disk_usage'] > 90:
            health_score -= 30
        elif performance_metrics['disk_usage'] > 80:
            health_score -= 15

        # Determine health status
        if health_score >= 90:
            health_status = 'excellent'
            health_color = 'success'
        elif health_score >= 75:
            health_status = 'good'
            health_color = 'info'
        elif health_score >= 60:
            health_status = 'fair'
            health_color = 'warning'
        else:
            health_status = 'poor'
            health_color = 'danger'

        # Log system health monitoring access
        log_system_health_check(
            user=request.user,
            health_score=health_score,
            system_metrics=performance_metrics,
            request=request
        )

        # Log admin data access for system monitoring
        log_admin_data_access(
            user=request.user,
            data_type='system_health',
            access_type='monitoring',
            filters_applied={
                'health_score': health_score,
                'health_status': health_status,
                'performance_metrics': performance_metrics,
                'db_stats_summary': {
                    'total_users': db_stats['users']['total'],
                    'total_venues': db_stats['venues']['total'],
                    'total_bookings': db_stats['bookings']['total']
                }
            },
            request=request
        )

        context = {
            'db_stats': db_stats,
            'performance_metrics': performance_metrics,
            'recent_events': recent_events,
            'app_health': app_health,
            'health_score': health_score,
            'health_status': health_status,
            'health_color': health_color,
        }

        return render(request, 'dashboard_app/admin/system_health.html', context)

    except Exception as e:
        log_error(
            error_type='admin_system_health_access',
            error_message="Error loading admin system health",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, 'There was an error loading system health data. Please try again.')
        # Temporarily re-raise exception for debugging
        raise e
        # return redirect('dashboard_app:admin_dashboard')
