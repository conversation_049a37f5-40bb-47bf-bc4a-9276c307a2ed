"""
Management command to seed venues_app with realistic test data.
Creates categories, venues, and services.
"""
import random
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.db import transaction
from accounts_app.models import ServiceProviderProfile
from venues_app.models import Category, Venue, Service


class Command(BaseCommand):
    """Seed venues_app with realistic test data."""
    
    help = 'Seed venues_app with categories, venues, and services'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing venues data before seeding',
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(
            self.style.SUCCESS('🌱 Starting venues_app data seeding...')
        )
        
        if options['clear']:
            self.clear_existing_data()
        
        with transaction.atomic():
            self.create_categories()
            self.create_venues()
            self.create_services()
        
        self.stdout.write(
            self.style.SUCCESS('✅ Venues app data seeding completed successfully!')
        )

    def clear_existing_data(self):
        """Clear existing venues data."""
        self.stdout.write('🧹 Clearing existing venues data...')

        try:
            # Delete in reverse order of dependencies
            Service.objects.all().delete()
            Venue.objects.all().delete()
            Category.objects.all().delete()

            self.stdout.write('   ✅ Existing venues data cleared')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'   ⚠️ Warning during data clearing: {str(e)}')
            )
            self.stdout.write('   ℹ️ Continuing with seeding...')

    def create_categories(self):
        """Create venue categories."""
        self.stdout.write('🏷️ Creating venue categories...')
        
        # Updated to match the 5 core categories
        categories_data = [
            {
                'name': 'Spa & Wellness',
                'description': 'Full-service spa facilities offering comprehensive wellness treatments, relaxation services, and rejuvenation experiences including facials, body treatments, and holistic therapies.'
            },
            {
                'name': 'Massage Therapy',
                'description': 'Professional massage therapy services including therapeutic massage, deep tissue, Swedish massage, hot stone, and specialized bodywork treatments for pain relief and relaxation.'
            },
            {
                'name': 'Beauty & Salon',
                'description': 'Beauty and salon services including hair styling, nail care, makeup application, eyebrow and lash treatments, and comprehensive beauty enhancement services.'
            },
            {
                'name': 'Medical Spa',
                'description': 'Medical aesthetic and wellness treatments combining medical expertise with spa luxury, including advanced skincare, anti-aging treatments, and medically supervised wellness services.'
            },
            {
                'name': 'Fitness & Wellness',
                'description': 'Fitness and wellness centers offering yoga classes, fitness training, wellness coaching, meditation sessions, and integrated health and fitness programs.'
            }
        ]
        
        created_count = 0
        for category_data in categories_data:
            category, created = Category.objects.get_or_create(
                category_name=category_data['name'],
                defaults={
                    'category_name': category_data['name'],
                    'category_description': category_data['description'],
                    'is_active': True
                }
            )
            if created:
                created_count += 1
                self.stdout.write(f'   ✅ Created category: {category.category_name}')
            else:
                # Update existing category to ensure it has proper description and is active
                if category.category_description != category_data['description'] or not category.is_active:
                    category.category_description = category_data['description']
                    category.is_active = True
                    category.save()
                    self.stdout.write(f'   🔄 Updated category: {category.category_name}')
                else:
                    self.stdout.write(f'   ⚠️ Category already exists: {category.category_name}')
        
        self.stdout.write(f'   📊 Created {created_count} new categories')

    def create_venues(self):
        """Create venues for service providers."""
        self.stdout.write('🏢 Creating venues...')
        
        # Get all service providers
        service_providers = list(ServiceProviderProfile.objects.all())
        if not service_providers:
            self.stdout.write(
                self.style.ERROR('❌ No service providers found. Run seed_data_accounts_app first.')
            )
            return
        
        categories = list(Category.objects.all())
        if not categories:
            self.stdout.write(
                self.style.ERROR('❌ No categories found. Creating categories first.')
            )
            self.create_categories()
            categories = list(Category.objects.all())
        
        # Venue name templates
        venue_names = [
            'Serenity Spa', 'Bliss Wellness Center', 'Tranquil Retreat', 'Harmony Day Spa',
            'Zen Garden Spa', 'Oasis Wellness', 'Pure Relaxation', 'Rejuvenation Station',
            'Peaceful Moments Spa', 'Luxury Wellness Lounge', 'Calm Waters Spa', 'Radiant Beauty',
            'Wellness Haven', 'Spa Sanctuary', 'Mindful Moments', 'Renewal Spa',
            'Golden Touch Spa', 'Serene Escape', 'Vitality Wellness', 'Tranquility Spa',
            'Healing Hands Massage', 'Therapeutic Touch', 'Muscle Relief Center', 'Deep Tissue Therapy',
            'Relaxation Station', 'Bodywork Boutique', 'Wellness Works', 'Stress Relief Spa',
            'Beauty Bliss Salon', 'Glamour Studio', 'Style Sanctuary', 'Elegance Salon',
            'Chic Beauty Bar', 'Luxe Hair Studio', 'Radiance Salon', 'Beauty Boutique',
            'Holistic Health Hub', 'Natural Wellness', 'Mind Body Soul', 'Integrative Wellness',
            'Balanced Living Spa', 'Organic Oasis', 'Pure Wellness Center', 'Healthy Glow Spa',
            'Urban Retreat Spa', 'City Wellness Center', 'Metropolitan Spa', 'Downtown Day Spa',
            'Neighborhood Wellness', 'Local Luxury Spa', 'Community Wellness Hub', 'Boutique Spa',
            'Medical Aesthetics Center', 'Advanced Wellness Clinic', 'Rejuvenation Medical Spa', 'Aesthetic Wellness',
            'Yoga & Wellness Studio', 'Mindfulness Center', 'Meditation & Spa', 'Spiritual Wellness',
            'Fitness Spa Fusion', 'Active Recovery Center', 'Athletic Wellness', 'Performance Spa',
            'Couples Retreat Spa', 'Romance Wellness', 'Intimate Spa Experience', 'Private Wellness',
            'Family Wellness Center', 'All Ages Spa', 'Generational Wellness', 'Family Day Spa',
            'Eco-Friendly Spa', 'Green Wellness Center', 'Sustainable Spa', 'Natural Elements Spa',
            'Luxury Resort Spa', 'Premium Wellness', 'Elite Spa Experience', 'VIP Wellness Center',
            'Express Wellness', 'Quick Spa Services', 'Lunch Break Spa', 'Efficient Wellness',
            'Seasonal Spa', 'Holiday Wellness', 'Special Occasion Spa', 'Celebration Wellness',
            'Therapeutic Wellness', 'Healing Arts Center', 'Recovery Spa', 'Rehabilitation Wellness',
            'Beauty & Wellness Co', 'Total Wellness Solutions', 'Complete Spa Experience', 'Full Service Wellness'
        ]
        
        # US cities and states for realistic addresses
        locations = [
            {'city': 'New York', 'state': 'NY', 'county': 'New York County'},
            {'city': 'Los Angeles', 'state': 'CA', 'county': 'Los Angeles County'},
            {'city': 'Chicago', 'state': 'IL', 'county': 'Cook County'},
            {'city': 'Houston', 'state': 'TX', 'county': 'Harris County'},
            {'city': 'Phoenix', 'state': 'AZ', 'county': 'Maricopa County'},
            {'city': 'Philadelphia', 'state': 'PA', 'county': 'Philadelphia County'},
            {'city': 'San Antonio', 'state': 'TX', 'county': 'Bexar County'},
            {'city': 'San Diego', 'state': 'CA', 'county': 'San Diego County'},
            {'city': 'Dallas', 'state': 'TX', 'county': 'Dallas County'},
            {'city': 'San Jose', 'state': 'CA', 'county': 'Santa Clara County'},
            {'city': 'Austin', 'state': 'TX', 'county': 'Travis County'},
            {'city': 'Jacksonville', 'state': 'FL', 'county': 'Duval County'},
            {'city': 'Fort Worth', 'state': 'TX', 'county': 'Tarrant County'},
            {'city': 'Columbus', 'state': 'OH', 'county': 'Franklin County'},
            {'city': 'Charlotte', 'state': 'NC', 'county': 'Mecklenburg County'},
            {'city': 'San Francisco', 'state': 'CA', 'county': 'San Francisco County'},
            {'city': 'Indianapolis', 'state': 'IN', 'county': 'Marion County'},
            {'city': 'Seattle', 'state': 'WA', 'county': 'King County'},
            {'city': 'Denver', 'state': 'CO', 'county': 'Denver County'},
            {'city': 'Boston', 'state': 'MA', 'county': 'Suffolk County'},
            {'city': 'El Paso', 'state': 'TX', 'county': 'El Paso County'},
            {'city': 'Detroit', 'state': 'MI', 'county': 'Wayne County'},
            {'city': 'Nashville', 'state': 'TN', 'county': 'Davidson County'},
            {'city': 'Portland', 'state': 'OR', 'county': 'Multnomah County'},
            {'city': 'Memphis', 'state': 'TN', 'county': 'Shelby County'},
        ]
        
        # Street names for addresses
        street_names = [
            'Main Street', 'Oak Avenue', 'Pine Road', 'Maple Drive', 'Cedar Lane',
            'Elm Street', 'Park Avenue', 'First Street', 'Second Avenue', 'Third Street',
            'Broadway', 'Market Street', 'Church Street', 'Spring Street', 'Washington Avenue',
            'Lincoln Road', 'Madison Street', 'Jefferson Avenue', 'Franklin Street', 'Adams Road',
            'Sunset Boulevard', 'Sunrise Avenue', 'Valley Road', 'Hill Street', 'River Drive',
            'Lake Avenue', 'Forest Street', 'Garden Road', 'Meadow Lane', 'Orchard Drive'
        ]
        
        created_count = 0
        
        # Create venues for existing service providers first
        for provider in service_providers:
            # Skip if venue already exists
            if hasattr(provider, 'venue') and provider.venue:
                self.stdout.write(f'   ⚠️ Venue for {provider.business_name} already exists, skipping...')
                continue
                
            location = random.choice(locations)
            venue_name = random.choice(venue_names)
            category = random.choice(categories)
            
            # Generate realistic address
            street_number = random.randint(100, 9999)
            street_name = random.choice(street_names)
            
            # Create venue description (some missing for realism)
            descriptions = [
                f"A premier wellness destination offering exceptional {category.name.lower()} services in a luxurious and tranquil environment.",
                f"Experience the ultimate in relaxation and rejuvenation at our state-of-the-art {category.name.lower()} facility.",
                f"Professional {category.name.lower()} services designed to restore your mind, body, and spirit.",
                f"Discover a sanctuary of wellness where expert therapists provide personalized {category.name.lower()} treatments.",
                "",  # Some venues have no description for realism
                f"Your local destination for premium {category.name.lower()} services and holistic wellness treatments.",
                f"Escape the everyday stress with our comprehensive {category.name.lower()} services and peaceful atmosphere.",
            ]
            
            # Operating hours (some missing for realism)
            operating_hours_options = [
                "Monday-Friday: 9:00 AM - 8:00 PM, Saturday: 9:00 AM - 6:00 PM, Sunday: 10:00 AM - 5:00 PM",
                "Monday-Saturday: 8:00 AM - 9:00 PM, Sunday: 10:00 AM - 6:00 PM",
                "Daily: 9:00 AM - 7:00 PM",
                "Monday-Friday: 10:00 AM - 7:00 PM, Weekends: 9:00 AM - 6:00 PM",
                "",  # Some venues have no operating hours listed
            ]
            
            # Tags for search optimization
            tag_options = [
                "relaxation, wellness, spa, massage, beauty",
                "therapeutic, healing, stress relief, rejuvenation",
                "luxury, premium, professional, expert care",
                "holistic, natural, organic, mindfulness",
                "couples, romantic, peaceful, tranquil",
                "",  # Some venues have no tags
            ]
            
            # Approval status (mostly approved, some pending/rejected for realism)
            approval_status_weights = [
                ('approved', 85),  # 85% approved
                ('pending', 10),   # 10% pending
                ('rejected', 5),   # 5% rejected
            ]
            approval_status = random.choices(
                [status for status, weight in approval_status_weights],
                weights=[weight for status, weight in approval_status_weights]
            )[0]
            
            # Visibility (mostly active, some inactive for realism)
            visibility = 'active' if random.random() < 0.9 else 'inactive'
            
            venue = Venue.objects.create(
                service_provider=provider,
                venue_name=venue_name,
                street_number=str(street_number),
                street_name=street_name,
                city=location['city'],
                county=location['county'],
                state=location['state'],
                short_description=random.choice(descriptions),
                operating_hours=random.choice(operating_hours_options),
                opening_notes=random.choice([
                    "Closed on major holidays",
                    "By appointment only on Sundays",
                    "Extended hours during holiday seasons",
                    "",  # Most have no special notes
                ]),
                tags=random.choice(tag_options),
                approval_status=approval_status,
                visibility=visibility,
            )
            
            # Add categories to venue
            venue.categories.add(category)
            
            created_count += 1
        
        self.stdout.write(f'   ✅ Created {created_count} venues for existing providers')

        # Create additional venues to reach 100 total
        total_venues_needed = 100
        current_venue_count = Venue.objects.count()
        additional_needed = max(0, total_venues_needed - current_venue_count)

        if additional_needed > 0:
            self.stdout.write(f'🏗️ Creating {additional_needed} additional venues to reach 100 total...')

            # Create additional service providers for the remaining venues
            additional_providers = []
            for i in range(additional_needed):
                # Create a user for the additional provider
                from django.contrib.auth import get_user_model
                User = get_user_model()

                email = f'additional_provider_{i+1}@example.com'
                user = User.objects.create_user(
                    email=email,
                    password='testpass123',
                    role=User.SERVICE_PROVIDER
                )

                # Create provider profile
                location = random.choice(locations)
                provider = ServiceProviderProfile.objects.create(
                    user=user,
                    legal_name=f'Additional Wellness Business {i+1}',
                    phone=f'+1{random.randint(**********, **********)}',
                    contact_name=f'Contact Person {i+1}',
                    address=f'{random.randint(100, 9999)} {random.choice(street_names)}',
                    city=location['city'],
                    state=location['state'],
                    county=location['county'],
                    zip_code=f'{random.randint(10000, 99999)}',
                )
                additional_providers.append(provider)

            # Create venues for additional providers
            for provider in additional_providers:
                location = random.choice(locations)
                venue_name = random.choice(venue_names)
                category = random.choice(categories)

                street_number = random.randint(100, 9999)
                street_name = random.choice(street_names)

                descriptions = [
                    f"A premier wellness destination offering exceptional {category.name.lower()} services.",
                    f"Professional {category.name.lower()} services in a relaxing environment.",
                    "",  # Some venues have no description
                ]

                approval_status_weights = [
                    ('approved', 80),
                    ('pending', 15),
                    ('rejected', 5),
                ]
                approval_status = random.choices(
                    [status for status, weight in approval_status_weights],
                    weights=[weight for status, weight in approval_status_weights]
                )[0]

                visibility = 'active' if random.random() < 0.85 else 'inactive'

                venue = Venue.objects.create(
                    service_provider=provider,
                    venue_name=venue_name,
                    street_number=str(street_number),
                    street_name=street_name,
                    city=location['city'],
                    county=location['county'],
                    state=location['state'],
                    short_description=random.choice(descriptions),
                    operating_hours=random.choice([
                        "Monday-Friday: 9:00 AM - 7:00 PM",
                        "Daily: 10:00 AM - 6:00 PM",
                        "",
                    ]),
                    tags=random.choice([
                        "wellness, relaxation, professional",
                        "spa, massage, beauty",
                        "",
                    ]),
                    approval_status=approval_status,
                    visibility=visibility,
                )

                venue.categories.add(category)
                created_count += 1

            self.stdout.write(f'   ✅ Created {additional_needed} additional venues')

        total_venues = Venue.objects.count()
        self.stdout.write(f'   📊 Total venues in database: {total_venues}')

    def create_services(self):
        """Create services for venues."""
        self.stdout.write('💆 Creating services for venues...')

        # Service templates with realistic pricing and duration
        service_templates = [
            # Massage Services
            {'service_title': 'Swedish Massage', 'short_description': 'Classic relaxation massage using long, flowing strokes to ease tension and promote relaxation.', 'price_min': Decimal('80.00'), 'price_max': Decimal('120.00'), 'duration_minutes': 60},
            {'service_title': 'Deep Tissue Massage', 'short_description': 'Therapeutic massage targeting deeper muscle layers to relieve chronic tension and pain.', 'price_min': Decimal('90.00'), 'price_max': Decimal('140.00'), 'duration_minutes': 60},
            {'service_title': 'Hot Stone Massage', 'short_description': 'Relaxing massage using heated stones to warm and loosen tight muscles.', 'price_min': Decimal('100.00'), 'price_max': Decimal('150.00'), 'duration_minutes': 75},
            {'service_title': 'Aromatherapy Massage', 'short_description': 'Soothing massage enhanced with essential oils for ultimate relaxation and stress relief.', 'price_min': Decimal('85.00'), 'price_max': Decimal('125.00'), 'duration_minutes': 60},
            {'service_title': 'Couples Massage', 'short_description': 'Romantic massage experience for two in a private room with side-by-side tables.', 'price_min': Decimal('200.00'), 'price_max': Decimal('300.00'), 'duration_minutes': 60},
            {'service_title': 'Prenatal Massage', 'short_description': 'Gentle, specialized massage designed for expecting mothers to relieve pregnancy discomfort.', 'price_min': Decimal('85.00'), 'price_max': Decimal('130.00'), 'duration_minutes': 60},
            {'service_title': 'Sports Massage', 'short_description': 'Targeted massage for athletes to enhance performance and aid in recovery.', 'price_min': Decimal('95.00'), 'price_max': Decimal('145.00'), 'duration_minutes': 60},

            # Facial Services
            {'service_title': 'Classic European Facial', 'short_description': 'Deep cleansing facial with exfoliation, extractions, and moisturizing for all skin types.', 'price_min': Decimal('75.00'), 'price_max': Decimal('110.00'), 'duration_minutes': 60},
            {'service_title': 'Anti-Aging Facial', 'short_description': 'Advanced facial treatment targeting fine lines and wrinkles with peptides and antioxidants.', 'price_min': Decimal('120.00'), 'price_max': Decimal('180.00'), 'duration_minutes': 75},
            {'service_title': 'Hydrating Facial', 'short_description': 'Intensive moisturizing treatment perfect for dry or dehydrated skin.', 'price_min': Decimal('80.00'), 'price_max': Decimal('120.00'), 'duration_minutes': 60},
            {'service_title': 'Acne Treatment Facial', 'short_description': 'Specialized facial for acne-prone skin with deep cleansing and purifying treatments.', 'price_min': Decimal('85.00'), 'price_max': Decimal('125.00'), 'duration_minutes': 60},
            {'service_title': 'Microdermabrasion', 'short_description': 'Exfoliating treatment that removes dead skin cells for smoother, brighter complexion.', 'price_min': Decimal('100.00'), 'price_max': Decimal('150.00'), 'duration_minutes': 45},

            # Body Treatments
            {'service_title': 'Body Wrap', 'short_description': 'Detoxifying body treatment that hydrates and firms skin while promoting relaxation.', 'price_min': Decimal('90.00'), 'price_max': Decimal('140.00'), 'duration_minutes': 60},
            {'service_title': 'Body Scrub', 'short_description': 'Exfoliating treatment that removes dead skin cells leaving skin soft and smooth.', 'price_min': Decimal('70.00'), 'price_max': Decimal('110.00'), 'duration_minutes': 45},
            {'service_title': 'Cellulite Treatment', 'short_description': 'Specialized treatment targeting cellulite with massage and firming products.', 'price_min': Decimal('110.00'), 'price_max': Decimal('160.00'), 'duration_minutes': 60},

            # Beauty Services
            {'service_title': 'Manicure', 'short_description': 'Complete nail care including shaping, cuticle care, and polish application.', 'price_min': Decimal('25.00'), 'price_max': Decimal('45.00'), 'duration_minutes': 45},
            {'service_title': 'Pedicure', 'short_description': 'Relaxing foot treatment with nail care, exfoliation, and massage.', 'price_min': Decimal('35.00'), 'price_max': Decimal('60.00'), 'duration_minutes': 60},
            {'service_title': 'Gel Manicure', 'short_description': 'Long-lasting manicure with gel polish that lasts up to two weeks.', 'price_min': Decimal('40.00'), 'price_max': Decimal('65.00'), 'duration_minutes': 60},
            {'service_title': 'Eyebrow Shaping', 'short_description': 'Professional eyebrow shaping and grooming for perfectly defined brows.', 'price_min': Decimal('20.00'), 'price_max': Decimal('40.00'), 'duration_minutes': 30},
            {'service_title': 'Eyelash Extensions', 'short_description': 'Individual lash extensions for fuller, longer lashes that last weeks.', 'price_min': Decimal('80.00'), 'price_max': Decimal('150.00'), 'duration_minutes': 120},

            # Hair Services
            {'service_title': 'Haircut & Style', 'short_description': 'Professional haircut with wash, cut, and styling for a fresh new look.', 'price_min': Decimal('45.00'), 'price_max': Decimal('85.00'), 'duration_minutes': 60},
            {'service_title': 'Hair Color', 'short_description': 'Full hair coloring service with professional products and expert application.', 'price_min': Decimal('80.00'), 'price_max': Decimal('150.00'), 'duration_minutes': 120},
            {'service_title': 'Highlights', 'short_description': 'Partial or full highlights to add dimension and brightness to your hair.', 'price_min': Decimal('90.00'), 'price_max': Decimal('180.00'), 'duration_minutes': 150},
            {'service_title': 'Deep Conditioning Treatment', 'short_description': 'Intensive hair treatment to restore moisture and repair damaged hair.', 'price_min': Decimal('30.00'), 'price_max': Decimal('60.00'), 'duration_minutes': 30},

            # Wellness Services
            {'service_title': 'Reflexology', 'short_description': 'Therapeutic foot massage targeting pressure points to promote overall wellness.', 'price_min': Decimal('60.00'), 'price_max': Decimal('90.00'), 'duration_minutes': 45},
            {'service_title': 'Reiki Healing', 'short_description': 'Energy healing session to promote relaxation and spiritual well-being.', 'price_min': Decimal('70.00'), 'price_max': Decimal('100.00'), 'duration_minutes': 60},
            {'service_title': 'Meditation Session', 'short_description': 'Guided meditation session for stress relief and mental clarity.', 'price_min': Decimal('40.00'), 'price_max': Decimal('70.00'), 'duration_minutes': 45},
            {'service_title': 'Yoga Class', 'short_description': 'Group or private yoga session for flexibility, strength, and mindfulness.', 'price_min': Decimal('25.00'), 'price_max': Decimal('80.00'), 'duration_minutes': 60},
            {'service_title': 'Sauna Session', 'short_description': 'Relaxing sauna experience to detoxify and rejuvenate the body.', 'price_min': Decimal('20.00'), 'price_max': Decimal('40.00'), 'duration_minutes': 30},
        ]

        venues = Venue.objects.all()
        total_services_created = 0

        for venue in venues:
            # Each venue gets 0-7 services
            num_services = random.randint(0, 7)

            if num_services == 0:
                self.stdout.write(f'   📝 {venue.venue_name}: No services')
                continue

            # Randomly select services for this venue
            selected_services = random.sample(service_templates, min(num_services, len(service_templates)))

            services_created_for_venue = 0
            for service_data in selected_services:
                # Some services might be inactive for realism
                is_active = random.random() < 0.9  # 90% active, 10% inactive

                # Some services might have single pricing instead of range
                if random.random() < 0.3:  # 30% chance of single pricing
                    price_max = None
                else:
                    price_max = service_data['price_max']

                Service.objects.create(
                    venue=venue,
                    service_title=service_data['service_title'],
                    short_description=service_data['short_description'],
                    price_min=service_data['price_min'],
                    price_max=price_max,
                    duration_minutes=service_data['duration_minutes'],
                    is_active=is_active
                )
                services_created_for_venue += 1
                total_services_created += 1

            self.stdout.write(f'   📝 {venue.venue_name}: {services_created_for_venue} services')

        self.stdout.write(f'   ✅ Created {total_services_created} services total')
