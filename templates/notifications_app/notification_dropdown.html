{% load static %}

<!-- Professional Notification Dropdown for Navigation Bar -->
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationDropdown" role="button"
       data-bs-toggle="dropdown" aria-expanded="false" title="Notifications">
        <i class="fas fa-bell"></i>
        {% if unread_notifications_count > 0 %}
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                  id="navNotificationBadge">
                {{ unread_notifications_count }}
                <span class="visually-hidden">unread notifications</span>
            </span>
        {% endif %}
    </a>

    <div class="dropdown-menu dropdown-menu-end professional-notification-dropdown"
         aria-labelledby="notificationDropdown">

        <!-- Professional Header -->
        <div class="notification-dropdown-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="notification-icon-header">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">Notifications</h6>
                        <small class="text-muted">Stay updated with your activity</small>
                    </div>
                </div>
                {% if unread_notifications_count > 0 %}
                    <span class="unread-count-badge" id="dropdownUnreadBadge">
                        {{ unread_notifications_count }}
                    </span>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        {% if unread_notifications_count > 0 %}
        <div class="notification-quick-actions">
            <form method="post" action="{% url 'notifications_app:mark_all_notifications_read' %}"
                  class="d-inline mark-all-read-form">
                {% csrf_token %}
                <button type="submit" class="quick-action-btn">
                    <i class="fas fa-check-double"></i>
                    Mark all read
                </button>
            </form>
        </div>
        {% endif %}

        <!-- Notification List -->
        <div class="notification-dropdown-body">
            {% if recent_notifications %}
                {% for notification in recent_notifications %}
                    <a href="{% if notification.action_url %}{{ notification.action_url }}{% else %}{% url 'notifications_app:notification_detail' notification.id %}{% endif %}" 
                       class="notification-item {{ notification.read_status }}">
                        <div class="notification-item-icon">
                            {% if notification.notification_type == 'review' %}
                                <div class="icon review-icon">
                                    <i class="fas fa-star"></i>
                                    {% if notification.related_object_type == 'ReviewResponse' %}
                                        <div class="response-indicator">
                                            <i class="fas fa-reply"></i>
                                        </div>
                                    {% endif %}
                                </div>
                            {% elif notification.notification_type == 'booking' %}
                                <div class="icon booking-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                            {% elif notification.notification_type == 'payment' %}
                                <div class="icon payment-icon">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                            {% else %}
                                <div class="icon system-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="notification-item-details">
                            <div class="notification-item-title">
                                {% if notification.read_status == 'unread' %}
                                    <span class="unread-indicator"></span>
                                {% endif %}
                                
                                {% if notification.notification_type == 'review' %}
                                    {% if notification.related_object_type == 'ReviewResponse' %}
                                        💬 Provider Response
                                    {% else %}
                                        ⭐ New Review
                                    {% endif %}
                                {% else %}
                                    {{ notification.title|truncatechars:35 }}
                                {% endif %}
                            </div>
                            <div class="notification-item-message">
                                {% if notification.notification_type == 'review' %}
                                    {% if notification.metadata.venue_name %}
                                        <span class="venue-highlight">{{ notification.metadata.venue_name }}</span>
                                    {% endif %}
                                    {% if notification.metadata.review_rating %}
                                        <span class="rating-preview">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= notification.metadata.review_rating %}★{% else %}☆{% endif %}
                                            {% endfor %}
                                        </span>
                                    {% endif %}
                                    <div class="message-preview">{{ notification.message|truncatechars:60 }}</div>
                                {% else %}
                                    {{ notification.message|truncatechars:70 }}
                                {% endif %}
                            </div>
                            <div class="notification-item-meta">
                                <span class="notification-type-badge {{ notification.notification_type }}">
                                    {% if notification.notification_type == 'review' %}
                                        {% if notification.related_object_type == 'ReviewResponse' %}
                                            Response
                                        {% else %}
                                            Review
                                        {% endif %}
                                    {% else %}
                                        {{ notification.get_notification_type_display }}
                                    {% endif %}
                                </span>
                                <span class="notification-time">
                                    {{ notification.created_at|timesince }} ago
                                </span>
                            </div>
                        </div>
                    </a>
                {% endfor %}
            {% else %}
                <div class="empty-notifications">
                    <div class="empty-icon">
                        <i class="fas fa-bell-slash"></i>
                    </div>
                    <div class="empty-text">
                        <h6>No notifications</h6>
                        <p>You're all caught up!</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Professional Footer -->
        <div class="notification-dropdown-footer">
            <a href="{% url 'notifications_app:notification_list' %}" class="view-all-btn">
                <i class="fas fa-list me-1"></i>View All Notifications
            </a>
        </div>
    </div>
</li>

<style>
/* Enhanced notification dropdown styles */
.professional-notification-dropdown {
    width: 380px;
    max-height: 500px;
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 0;
    overflow: hidden;
}

.notification-dropdown-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-icon-header {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
}

.unread-count-badge {
    background: #dc3545;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
}

.notification-quick-actions {
    padding: 12px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.quick-action-btn {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 12px;
    cursor: pointer;
    transition: color 0.2s ease;
}

.quick-action-btn:hover {
    color: #495057;
}

.notification-dropdown-body {
    max-height: 300px;
    overflow-y: auto;
    padding: 8px 0;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 20px;
    text-decoration: none;
    color: inherit;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
    position: relative;
}

.notification-item:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: inherit;
}

.notification-item.unread {
    background-color: #fff3cd;
    border-left: 3px solid #ffc107;
}

.notification-item-icon {
    margin-right: 12px;
    flex-shrink: 0;
}

.icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    position: relative;
}

.icon.review-icon {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

.icon.booking-icon {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.icon.payment-icon {
    background: linear-gradient(135deg, #007bff 0%, #6f42c1 100%);
    color: white;
}

.icon.system-icon {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

.response-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: white;
    border: 2px solid white;
}

.notification-item-details {
    flex: 1;
    min-width: 0;
}

.notification-item-title {
    font-weight: 600;
    font-size: 14px;
    color: #2c3e50;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.unread-indicator {
    width: 8px;
    height: 8px;
    background: #dc3545;
    border-radius: 50%;
    flex-shrink: 0;
}

.venue-highlight {
    font-weight: 600;
    color: #2c3e50;
    font-size: 12px;
}

.rating-preview {
    color: #ffc107;
    font-size: 11px;
    margin-left: 4px;
}

.message-preview {
    color: #6c757d;
    font-size: 12px;
    margin-top: 2px;
}

.notification-item-message {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
    margin-bottom: 4px;
}

.notification-item-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 4px;
}

.notification-type-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.notification-type-badge.review {
    background: #fff3cd;
    color: #856404;
}

.notification-type-badge.booking {
    background: #d4edda;
    color: #155724;
}

.notification-type-badge.payment {
    background: #cce7ff;
    color: #004085;
}

.notification-type-badge.system {
    background: #e2e3e5;
    color: #383d41;
}

.notification-time {
    font-size: 10px;
    color: #adb5bd;
}

.empty-notifications {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-text h6 {
    margin-bottom: 8px;
    color: #495057;
}

.empty-text p {
    font-size: 14px;
    margin: 0;
}

.notification-dropdown-footer {
    padding: 12px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.view-all-btn {
    color: #6c757d;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: color 0.2s ease;
}

.view-all-btn:hover {
    color: #495057;
    text-decoration: none;
}

/* Scrollbar styling */
.notification-dropdown-body::-webkit-scrollbar {
    width: 4px;
}

.notification-dropdown-body::-webkit-scrollbar-track {
    background: #f8f9fa;
}

.notification-dropdown-body::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 4px;
}

.notification-dropdown-body::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

/* Mobile responsive */
@media (max-width: 576px) {
    .professional-notification-dropdown {
        width: 320px;
        margin-right: -20px;
    }
    
    .notification-item {
        padding: 10px 16px;
    }
    
    .notification-dropdown-header {
        padding: 16px;
    }
    
    .icon {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
    
    .notification-item-title {
        font-size: 13px;
    }
    
    .notification-item-message {
        font-size: 11px;
    }
}
</style>
