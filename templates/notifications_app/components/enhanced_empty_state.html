<!-- Enhanced Empty State Component for Notifications -->
<div class="enhanced-empty-state" data-empty-type="{{ empty_type|default:'notifications' }}">
    <div class="empty-state-container">
        <!-- Animated Illustration -->
        <div class="empty-illustration">
            {% if empty_type == 'notifications' %}
                <div class="notification-illustration">
                    <div class="bell-container">
                        <div class="bell">
                            <div class="bell-body"></div>
                            <div class="bell-clapper"></div>
                        </div>
                        <div class="sound-waves">
                            <div class="wave wave-1"></div>
                            <div class="wave wave-2"></div>
                            <div class="wave wave-3"></div>
                        </div>
                    </div>
                </div>
            {% elif empty_type == 'reviews' %}
                <div class="review-illustration">
                    <div class="stars-container">
                        <div class="star star-1">⭐</div>
                        <div class="star star-2">⭐</div>
                        <div class="star star-3">⭐</div>
                        <div class="star star-4">⭐</div>
                        <div class="star star-5">⭐</div>
                    </div>
                </div>
            {% elif empty_type == 'bookings' %}
                <div class="booking-illustration">
                    <div class="calendar-container">
                        <div class="calendar">
                            <div class="calendar-header"></div>
                            <div class="calendar-grid">
                                <div class="calendar-day"></div>
                                <div class="calendar-day"></div>
                                <div class="calendar-day active"></div>
                                <div class="calendar-day"></div>
                            </div>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="default-illustration">
                    <div class="placeholder-circle">
                        <i class="fas fa-inbox"></i>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Content -->
        <div class="empty-content">
            <h3 class="empty-title">
                {% if empty_type == 'notifications' %}
                    You're All Caught Up! 🎉
                {% elif empty_type == 'reviews' %}
                    No Reviews Yet
                {% elif empty_type == 'bookings' %}
                    No Bookings Found
                {% else %}
                    Nothing Here Yet
                {% endif %}
            </h3>
            
            <p class="empty-description">
                {% if empty_type == 'notifications' %}
                    Great job staying on top of everything! When you have new bookings, payments, or reviews, they'll appear here to keep you informed.
                {% elif empty_type == 'reviews' %}
                    Once customers start leaving reviews for your services, you'll see them here. Great reviews help build trust with future customers!
                {% elif empty_type == 'bookings' %}
                    No bookings match your current criteria. Try adjusting your filters or check back later for new reservations.
                {% else %}
                    This area will populate with content as you use the platform.
                {% endif %}
            </p>

            <!-- Feature Highlights -->
            <div class="feature-highlights">
                {% if empty_type == 'notifications' %}
                    <div class="feature-item">
                        <div class="feature-icon booking">📅</div>
                        <span>Booking Updates</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon payment">💳</div>
                        <span>Payment Alerts</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon review">⭐</div>
                        <span>Review Notifications</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon system">🔔</div>
                        <span>System Updates</span>
                    </div>
                {% elif empty_type == 'reviews' %}
                    <div class="feature-item">
                        <div class="feature-icon star">⭐</div>
                        <span>Customer Ratings</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon comment">💬</div>
                        <span>Written Feedback</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon response">🔄</div>
                        <span>Response Management</span>
                    </div>
                {% elif empty_type == 'bookings' %}
                    <div class="feature-item">
                        <div class="feature-icon calendar">📋</div>
                        <span>Reservation Details</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon time">⏰</div>
                        <span>Scheduling Info</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon customer">👤</div>
                        <span>Customer Details</span>
                    </div>
                {% endif %}
            </div>

            <!-- Action Buttons -->
            <div class="empty-actions">
                {% if empty_type == 'notifications' %}
                    {% if user.role == 'customer' %}
                        <a href="{% url 'venues_app:venue_list' %}" class="empty-action-btn primary">
                            <i class="fas fa-search"></i>
                            <span>Discover Venues</span>
                        </a>
                        <a href="{% url 'booking_cart_app:cart_view' %}" class="empty-action-btn secondary">
                            <i class="fas fa-shopping-cart"></i>
                            <span>View Cart</span>
                        </a>
                    {% elif user.role == 'service_provider' %}
                        <a href="{% url 'dashboard_app:provider_dashboard' %}" class="empty-action-btn primary">
                            <i class="fas fa-chart-line"></i>
                            <span>Provider Dashboard</span>
                        </a>
                        <a href="{% url 'venues_app:venue_list' %}" class="empty-action-btn secondary">
                            <i class="fas fa-store"></i>
                            <span>Manage Venues</span>
                        </a>
                    {% endif %}
                {% elif empty_type == 'reviews' %}
                    <a href="{% url 'venues_app:venue_list' %}" class="empty-action-btn primary">
                        <i class="fas fa-star"></i>
                        <span>Improve Your Service</span>
                    </a>
                    <a href="{% url 'dashboard_app:provider_dashboard' %}" class="empty-action-btn secondary">
                        <i class="fas fa-chart-bar"></i>
                        <span>View Analytics</span>
                    </a>
                {% elif empty_type == 'bookings' %}
                    <a href="{% url 'venues_app:venue_list' %}" class="empty-action-btn primary">
                        <i class="fas fa-plus"></i>
                        <span>Add New Venue</span>
                    </a>
                    <a href="#" class="empty-action-btn secondary" onclick="clearFilters()">
                        <i class="fas fa-filter"></i>
                        <span>Clear Filters</span>
                    </a>
                {% endif %}
            </div>

            <!-- Tips Section -->
            <div class="empty-tips">
                {% if empty_type == 'notifications' %}
                    <div class="tip-item">
                        <i class="fas fa-lightbulb"></i>
                        <span>Enable browser notifications to stay updated instantly</span>
                    </div>
                {% elif empty_type == 'reviews' %}
                    <div class="tip-item">
                        <i class="fas fa-lightbulb"></i>
                        <span>Encourage customers to leave reviews by providing exceptional service</span>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.enhanced-empty-state {
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
    border-radius: 1rem;
    border: 2px dashed rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.enhanced-empty-state::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="empty-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23f0f0f0" opacity="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23empty-pattern)"/></svg>') repeat;
    opacity: 0.3;
    z-index: 1;
}

.empty-state-container {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 600px;
    width: 100%;
}

/* Illustrations */
.empty-illustration {
    margin-bottom: 2rem;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Notification Bell Animation */
.bell-container {
    position: relative;
}

.bell {
    width: 60px;
    height: 60px;
    position: relative;
    animation: bellRing 3s ease-in-out infinite;
}

.bell-body {
    width: 60px;
    height: 50px;
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
    border-radius: 50% 50% 0 0;
    position: relative;
}

.bell-body::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 8px;
    background: #374151;
    border-radius: 4px;
}

.bell-clapper {
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 15px;
    background: #fbbf24;
    border-radius: 50%;
    animation: clapperSwing 3s ease-in-out infinite;
}

.sound-waves {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.wave {
    position: absolute;
    border: 2px solid #fbbf24;
    border-radius: 50%;
    opacity: 0;
}

.wave-1 {
    width: 80px;
    height: 80px;
    top: -40px;
    left: -40px;
    animation: waveExpand 3s ease-in-out infinite;
}

.wave-2 {
    width: 100px;
    height: 100px;
    top: -50px;
    left: -50px;
    animation: waveExpand 3s ease-in-out infinite 0.3s;
}

.wave-3 {
    width: 120px;
    height: 120px;
    top: -60px;
    left: -60px;
    animation: waveExpand 3s ease-in-out infinite 0.6s;
}

/* Review Stars Animation */
.stars-container {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.star {
    font-size: 24px;
    animation: starTwinkle 2s ease-in-out infinite;
}

.star-1 { animation-delay: 0s; }
.star-2 { animation-delay: 0.2s; }
.star-3 { animation-delay: 0.4s; }
.star-4 { animation-delay: 0.6s; }
.star-5 { animation-delay: 0.8s; }

/* Calendar Animation */
.calendar-container {
    perspective: 1000px;
}

.calendar {
    width: 80px;
    height: 80px;
    background: white;
    border: 3px solid #d1d5db;
    border-radius: 8px;
    animation: calendarFloat 3s ease-in-out infinite;
    transform-style: preserve-3d;
}

.calendar-header {
    height: 20px;
    background: #ef4444;
    border-radius: 4px 4px 0 0;
    margin-bottom: 8px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4px;
    padding: 8px;
}

.calendar-day {
    width: 12px;
    height: 12px;
    background: #f3f4f6;
    border-radius: 2px;
}

.calendar-day.active {
    background: #3b82f6;
    animation: dayPulse 2s ease-in-out infinite;
}

/* Content Styles */
.empty-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    font-family: 'Poppins', sans-serif;
}

.empty-description {
    font-size: 1.1rem;
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Feature Highlights */
.feature-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
}

.feature-icon.booking { background: #dbeafe; color: #1d4ed8; }
.feature-icon.payment { background: #dcfce7; color: #16a34a; }
.feature-icon.review { background: #fef3c7; color: #d97706; }
.feature-icon.system { background: #f3e8ff; color: #7c3aed; }
.feature-icon.star { background: #fef3c7; color: #d97706; }
.feature-icon.comment { background: #dbeafe; color: #1d4ed8; }
.feature-icon.response { background: #dcfce7; color: #16a34a; }
.feature-icon.calendar { background: #dbeafe; color: #1d4ed8; }
.feature-icon.time { background: #fef3c7; color: #d97706; }
.feature-icon.customer { background: #f3e8ff; color: #7c3aed; }

.feature-item span {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    text-align: center;
}

/* Action Buttons */
.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.empty-action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.empty-action-btn.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.empty-action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    color: white;
}

.empty-action-btn.secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;
}

.empty-action-btn.secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-2px);
    color: #374151;
}

/* Tips */
.empty-tips {
    max-width: 400px;
    margin: 0 auto;
}

.tip-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: #fef7f0;
    border: 1px solid #fed7aa;
    border-radius: 0.5rem;
    color: #92400e;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.tip-item i {
    color: #d97706;
}

/* Animations */
@keyframes bellRing {
    0%, 100% { transform: rotate(0deg); }
    10%, 30% { transform: rotate(-10deg); }
    20%, 40% { transform: rotate(10deg); }
    50%, 90% { transform: rotate(0deg); }
}

@keyframes clapperSwing {
    0%, 100% { transform: translateX(-50%) rotate(0deg); }
    25% { transform: translateX(-50%) rotate(-15deg); }
    75% { transform: translateX(-50%) rotate(15deg); }
}

@keyframes waveExpand {
    0% { opacity: 0; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1); }
    100% { opacity: 0; transform: scale(1.2); }
}

@keyframes starTwinkle {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 0.7; }
}

@keyframes calendarFloat {
    0%, 100% { transform: translateY(0px) rotateX(0deg); }
    50% { transform: translateY(-10px) rotateX(10deg); }
}

@keyframes dayPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.3); }
}

/* Responsive */
@media (max-width: 768px) {
    .enhanced-empty-state {
        min-height: 400px;
        padding: 2rem 1rem;
    }
    
    .empty-title {
        font-size: 1.5rem;
    }
    
    .empty-description {
        font-size: 1rem;
    }
    
    .feature-highlights {
        grid-template-columns: repeat(2, 1fr);
        max-width: 300px;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .empty-action-btn {
        width: 200px;
        justify-content: center;
    }
}
</style>

<script>
function clearFilters() {
    // Clear filters and refresh page
    const url = new URL(window.location);
    url.search = '';
    window.location.href = url.toString();
}

// Initialize empty state animations
document.addEventListener('DOMContentLoaded', function() {
    const emptyState = document.querySelector('.enhanced-empty-state');
    if (emptyState) {
        // Add entrance animation
        emptyState.style.opacity = '0';
        emptyState.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            emptyState.style.transition = 'all 0.6s ease';
            emptyState.style.opacity = '1';
            emptyState.style.transform = 'translateY(0)';
        }, 100);
        
        // Add stagger animation to feature items
        const featureItems = emptyState.querySelectorAll('.feature-item');
        featureItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                item.style.transition = 'all 0.4s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, 200 + (index * 100));
        });
    }
});
</script> 