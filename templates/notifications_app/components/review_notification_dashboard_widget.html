{% load static %}

<!-- Review Notifications Dashboard Widget -->
<div class="review-notification-widget" id="reviewNotificationWidget">
    <div class="widget-header">
        <div class="widget-title">
            <div class="title-icon">
                <i class="fas fa-star"></i>
            </div>
            <div class="title-text">
                <h6>Review Notifications</h6>
                <span class="subtitle">Recent customer feedback</span>
            </div>
        </div>
        <div class="widget-actions">
            {% if unread_review_notifications > 0 %}
                <span class="notification-count-badge">{{ unread_review_notifications }}</span>
            {% endif %}
            <a href="{% url 'notifications_app:notification_list' %}?notification_type=review" class="view-all-link" title="View All Review Notifications">
                <i class="fas fa-external-link-alt"></i>
            </a>
        </div>
    </div>

    <div class="widget-body">
        {% if review_notifications %}
            <div class="notification-list">
                {% for notification in review_notifications %}
                    <div class="review-notification-item {% if notification.read_status == 'unread' %}unread{% endif %}">
                        <div class="notification-content">
                            <div class="notification-header">
                                <div class="notification-type-info">
                                    {% if notification.related_object_type == 'ReviewResponse' %}
                                        <div class="type-icon response">
                                            <i class="fas fa-reply"></i>
                                        </div>
                                        <span class="type-label">Response Sent</span>
                                    {% else %}
                                        <div class="type-icon review">
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <span class="type-label">New Review</span>
                                    {% endif %}
                                </div>
                                {% if notification.read_status == 'unread' %}
                                    <span class="new-badge">NEW</span>
                                {% endif %}
                            </div>

                            <div class="notification-details">
                                {% if notification.metadata.venue_name %}
                                    <div class="venue-name">{{ notification.metadata.venue_name }}</div>
                                {% endif %}
                                
                                {% if notification.metadata.customer_name %}
                                    <div class="customer-info">
                                        <span class="customer-name">{{ notification.metadata.customer_name }}</span>
                                        {% if notification.metadata.review_rating %}
                                            <div class="rating-display">
                                                {% for i in "12345" %}
                                                    {% if forloop.counter <= notification.metadata.review_rating %}
                                                        <span class="star filled">★</span>
                                                    {% else %}
                                                        <span class="star empty">☆</span>
                                                    {% endif %}
                                                {% endfor %}
                                                <span class="rating-number">{{ notification.metadata.review_rating }}/5</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endif %}

                                {% if notification.metadata.review_preview %}
                                    <div class="review-preview">
                                        "{{ notification.metadata.review_preview|truncatechars:80 }}"
                                    </div>
                                {% endif %}

                                <div class="notification-meta">
                                    <span class="timestamp">{{ notification.created_at|timesince }} ago</span>
                                    {% if notification.metadata.notification_priority == 'high' %}
                                        <span class="priority-badge high">High Priority</span>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="notification-actions">
                                {% if notification.related_object_type == 'Review' %}
                                    {% if notification.action_url %}
                                        <a href="{{ notification.action_url }}" class="action-btn primary" title="View Review">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    {% endif %}
                                    {% if not notification.metadata.has_response %}
                                        <a href="{% url 'review_app:provider_respond_to_review' notification.related_object_id %}" class="action-btn success" title="Respond">
                                            <i class="fas fa-reply"></i>
                                        </a>
                                    {% endif %}
                                {% else %}
                                    {% if notification.action_url %}
                                        <a href="{{ notification.action_url }}" class="action-btn info" title="View Response">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    {% endif %}
                                {% endif %}
                                
                                <form method="post" action="{% if notification.read_status == 'unread' %}{% url 'notifications_app:mark_notification_read' notification.id %}{% else %}{% url 'notifications_app:mark_notification_unread' notification.id %}{% endif %}" class="mark-read-form">
                                    {% csrf_token %}
                                    <button type="submit" class="action-btn outline" title="{% if notification.read_status == 'unread' %}Mark as Read{% else %}Mark as Unread{% endif %}">
                                        <i class="fas {% if notification.read_status == 'unread' %}fa-envelope-open{% else %}fa-envelope{% endif %}"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="empty-text">
                    <h6>No review notifications</h6>
                    <p>You'll see customer reviews and responses here</p>
                </div>
            </div>
        {% endif %}
    </div>

    {% if review_notifications %}
        <div class="widget-footer">
            <a href="{% url 'review_app:provider_venue_reviews' %}" class="footer-link">
                <i class="fas fa-chart-bar me-1"></i>View Review Dashboard
            </a>
            <a href="{% url 'notifications_app:notification_list' %}?notification_type=review" class="footer-link">
                <i class="fas fa-list me-1"></i>All Notifications
            </a>
        </div>
    {% endif %}
</div>

<style>
.review-notification-widget {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.review-notification-widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    border-color: #ffc107;
}

.widget-header {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.widget-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.title-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.title-text h6 {
    margin: 0;
    font-weight: 600;
    font-size: 16px;
}

.subtitle {
    font-size: 12px;
    opacity: 0.9;
}

.widget-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-count-badge {
    background: #dc3545;
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.view-all-link {
    color: white;
    font-size: 14px;
    text-decoration: none;
    padding: 6px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.view-all-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
}

.widget-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.review-notification-item {
    border: 2px solid #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.review-notification-item.unread {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
}

.review-notification-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notification-content {
    padding: 16px;
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.notification-type-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.type-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
}

.type-icon.review {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.type-icon.response {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.type-label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 13px;
}

.new-badge {
    background: #dc3545;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.notification-details {
    margin-bottom: 12px;
}

.venue-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 6px;
    font-size: 14px;
}

.customer-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    flex-wrap: wrap;
    gap: 8px;
}

.customer-name {
    color: #6c757d;
    font-size: 13px;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 2px;
}

.star.filled {
    color: #ffc107;
    font-size: 12px;
}

.star.empty {
    color: #dee2e6;
    font-size: 12px;
}

.rating-number {
    margin-left: 4px;
    font-size: 11px;
    color: #6c757d;
    font-weight: 500;
}

.review-preview {
    background: #f8f9fa;
    border-left: 3px solid #007bff;
    padding: 8px 12px;
    border-radius: 4px;
    font-style: italic;
    color: #495057;
    font-size: 12px;
    margin: 8px 0;
}

.notification-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
}

.timestamp {
    color: #6c757d;
    font-size: 11px;
}

.priority-badge.high {
    background: #dc3545;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 9px;
    font-weight: 600;
    text-transform: uppercase;
}

.notification-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
    justify-content: flex-end;
}

.action-btn {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 11px;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.action-btn.primary {
    background: #007bff;
    color: white;
}

.action-btn.primary:hover {
    background: #0056b3;
    color: white;
}

.action-btn.success {
    background: #28a745;
    color: white;
}

.action-btn.success:hover {
    background: #1e7e34;
    color: white;
}

.action-btn.info {
    background: #17a2b8;
    color: white;
}

.action-btn.info:hover {
    background: #138496;
    color: white;
}

.action-btn.outline {
    background: transparent;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.action-btn.outline:hover {
    background: #f8f9fa;
    color: #495057;
}

.mark-read-form {
    display: inline;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
    color: #ffc107;
}

.empty-text h6 {
    margin-bottom: 8px;
    color: #495057;
}

.empty-text p {
    font-size: 14px;
    margin: 0;
}

.widget-footer {
    background: #f8f9fa;
    padding: 12px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-link {
    color: #6c757d;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    transition: color 0.2s ease;
}

.footer-link:hover {
    color: #495057;
    text-decoration: none;
}

/* Scrollbar styling */
.widget-body::-webkit-scrollbar {
    width: 4px;
}

.widget-body::-webkit-scrollbar-track {
    background: #f8f9fa;
}

.widget-body::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 4px;
}

.widget-body::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

/* Mobile responsive */
@media (max-width: 576px) {
    .widget-header {
        padding: 12px 16px;
    }
    
    .widget-body {
        padding: 16px;
    }
    
    .notification-content {
        padding: 12px;
    }
    
    .customer-info {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .notification-actions {
        gap: 6px;
    }
    
    .action-btn {
        width: 24px;
        height: 24px;
        font-size: 10px;
    }
    
    .widget-footer {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
}
</style>

<script>
// Enhanced notification handling for review widget
document.addEventListener('DOMContentLoaded', function() {
    const widget = document.getElementById('reviewNotificationWidget');
    if (!widget) return;

    // Handle mark as read/unread forms
    const markReadForms = widget.querySelectorAll('.mark-read-form');
    markReadForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(form);
            const url = form.getAttribute('action');
            const button = form.querySelector('button');
            const originalIcon = button.innerHTML;
            
            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;
            
            fetch(url, {
                method: 'POST',
                body: formData,
                headers: { 'X-Requested-With': 'XMLHttpRequest' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the notification item
                    const notificationItem = form.closest('.review-notification-item');
                    if (notificationItem) {
                        if (form.action.includes('mark_notification_read')) {
                            notificationItem.classList.remove('unread');
                            button.innerHTML = '<i class="fas fa-envelope"></i>';
                            form.action = form.action.replace('mark_notification_read', 'mark_notification_unread');
                        } else {
                            notificationItem.classList.add('unread');
                            button.innerHTML = '<i class="fas fa-envelope-open"></i>';
                            form.action = form.action.replace('mark_notification_unread', 'mark_notification_read');
                        }
                    }
                    
                    // Update badge count
                    if (data.unread_count !== undefined) {
                        const badge = widget.querySelector('.notification-count-badge');
                        if (badge) {
                            if (data.unread_count > 0) {
                                badge.textContent = data.unread_count;
                                badge.style.display = 'inline-block';
                            } else {
                                badge.style.display = 'none';
                            }
                        }
                    }
                } else {
                    button.innerHTML = originalIcon;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                button.innerHTML = originalIcon;
            })
            .finally(() => {
                button.disabled = false;
            });
        });
    });
    
    // Auto-refresh widget every 30 seconds for new notifications
    setInterval(function() {
        // Only refresh if the page is visible
        if (!document.hidden) {
            const currentCount = widget.querySelector('.notification-count-badge')?.textContent || 0;
            
            // Simple check for new notifications (you might want to implement a proper API endpoint)
            fetch('/notifications/unread/?notification_type=review', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.unread_count > currentCount) {
                    // New notification arrived - show visual indicator
                    widget.style.animation = 'newNotificationPulse 1s ease-in-out';
                    setTimeout(() => {
                        widget.style.animation = '';
                    }, 1000);
                }
            })
            .catch(error => {
                console.log('Auto-refresh failed:', error);
            });
        }
    }, 30000); // 30 seconds
});

// CSS animation for new notification pulse
const style = document.createElement('style');
style.textContent = `
@keyframes newNotificationPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); box-shadow: 0 0 20px rgba(255, 193, 7, 0.4); }
}
`;
document.head.appendChild(style);
</script> 