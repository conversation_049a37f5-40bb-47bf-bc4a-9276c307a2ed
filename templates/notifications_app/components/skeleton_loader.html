<!-- Enhanced Skeleton Loading Component for Notifications -->
<div class="skeleton-container" data-skeleton-type="{{ skeleton_type|default:'notifications' }}">
    {% if skeleton_type == 'notifications' or not skeleton_type %}
        <!-- Notification List Skeleton -->
        <div class="notification-skeleton-wrapper">
            {% for i in "123456" %}
            <div class="notification-skeleton-item" style="animation-delay: {{ forloop.counter0|mul:0.1 }}s;">
                <div class="notification-skeleton-content">
                    <!-- Icon skeleton -->
                    <div class="skeleton-icon"></div>
                    
                    <!-- Content skeleton -->
                    <div class="skeleton-content">
                        <!-- Title and badge -->
                        <div class="skeleton-header">
                            <div class="skeleton-title"></div>
                            <div class="skeleton-badge"></div>
                        </div>
                        
                        <!-- Message -->
                        <div class="skeleton-message">
                            <div class="skeleton-line"></div>
                            <div class="skeleton-line short"></div>
                        </div>
                        
                        <!-- Meta info -->
                        <div class="skeleton-meta">
                            <div class="skeleton-time"></div>
                            <div class="skeleton-category"></div>
                        </div>
                    </div>
                    
                    <!-- Actions skeleton -->
                    <div class="skeleton-actions">
                        <div class="skeleton-action-btn"></div>
                        <div class="skeleton-action-btn"></div>
                        <div class="skeleton-action-btn"></div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    
    {% elif skeleton_type == 'notification_detail' %}
        <!-- Single Notification Detail Skeleton -->
        <div class="notification-detail-skeleton">
            <div class="skeleton-header-large">
                <div class="skeleton-icon-large"></div>
                <div class="skeleton-title-large"></div>
                <div class="skeleton-badge-large"></div>
            </div>
            
            <div class="skeleton-content-area">
                <div class="skeleton-paragraph">
                    <div class="skeleton-line"></div>
                    <div class="skeleton-line"></div>
                    <div class="skeleton-line medium"></div>
                </div>
                
                <div class="skeleton-details-box">
                    <div class="skeleton-detail-row">
                        <div class="skeleton-label"></div>
                        <div class="skeleton-value"></div>
                    </div>
                    <div class="skeleton-detail-row">
                        <div class="skeleton-label"></div>
                        <div class="skeleton-value"></div>
                    </div>
                    <div class="skeleton-detail-row">
                        <div class="skeleton-label"></div>
                        <div class="skeleton-value long"></div>
                    </div>
                </div>
                
                <div class="skeleton-action-area">
                    <div class="skeleton-primary-btn"></div>
                    <div class="skeleton-secondary-btn"></div>
                </div>
            </div>
        </div>
    
    {% elif skeleton_type == 'dashboard_cards' %}
        <!-- Dashboard Cards Skeleton -->
        <div class="dashboard-skeleton-grid">
            {% for i in "1234" %}
            <div class="dashboard-card-skeleton" style="animation-delay: {{ forloop.counter0|mul:0.15 }}s;">
                <div class="skeleton-card-header">
                    <div class="skeleton-card-icon"></div>
                    <div class="skeleton-card-title"></div>
                </div>
                <div class="skeleton-card-content">
                    <div class="skeleton-number"></div>
                    <div class="skeleton-label-small"></div>
                </div>
                <div class="skeleton-card-footer">
                    <div class="skeleton-trend"></div>
                </div>
            </div>
            {% endfor %}
        </div>
        
    {% elif skeleton_type == 'review_notifications' %}
        <!-- Review Notifications Skeleton -->
        <div class="review-skeleton-wrapper">
            {% for i in "123" %}
            <div class="review-skeleton-item" style="animation-delay: {{ forloop.counter0|mul:0.12 }}s;">
                <div class="skeleton-review-header">
                    <div class="skeleton-avatar"></div>
                    <div class="skeleton-review-info">
                        <div class="skeleton-customer-name"></div>
                        <div class="skeleton-venue-name"></div>
                        <div class="skeleton-stars">
                            <div class="skeleton-star"></div>
                            <div class="skeleton-star"></div>
                            <div class="skeleton-star"></div>
                            <div class="skeleton-star"></div>
                            <div class="skeleton-star"></div>
                        </div>
                    </div>
                </div>
                <div class="skeleton-review-content">
                    <div class="skeleton-line"></div>
                    <div class="skeleton-line medium"></div>
                </div>
                <div class="skeleton-review-actions">
                    <div class="skeleton-action-btn small"></div>
                    <div class="skeleton-action-btn small"></div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% endif %}
</div>

<style>
/* Base Skeleton Styles */
.skeleton-container {
    width: 100%;
    animation: containerFadeIn 0.6s ease-out;
}

@keyframes containerFadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Base skeleton element */
.skeleton-base {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
}

@keyframes shimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Apply skeleton base to all skeleton elements */
[class*="skeleton-"]:not(.skeleton-container):not(.skeleton-wrapper):not(.skeleton-item) {
    @extend .skeleton-base;
}

/* Enhanced shimmer effect */
.skeleton-container [class*="skeleton-"]:not(.skeleton-container):not(.skeleton-wrapper):not(.skeleton-item) {
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.4) 20%, 
        rgba(255, 255, 255, 0.8) 50%, 
        rgba(255, 255, 255, 0.4) 80%, 
        transparent 100%
    ), linear-gradient(90deg, #f0f0f0 0%, #e8e8e8 50%, #f0f0f0 100%);
    background-size: 200% 100%, 100% 100%;
    animation: enhancedShimmer 2s infinite ease-in-out;
    border-radius: 6px;
}

@keyframes enhancedShimmer {
    0% { background-position: 200% 0, 0 0; }
    100% { background-position: -200% 0, 0 0; }
}

/* Notification List Skeleton */
.notification-skeleton-wrapper {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.notification-skeleton-item {
    padding: 16px;
    background: white;
    border: 2px solid #f0f0f0;
    border-radius: 12px;
    animation: itemSlideIn 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes itemSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notification-skeleton-content {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.skeleton-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    flex-shrink: 0;
}

.skeleton-content {
    flex: 1;
    min-width: 0;
}

.skeleton-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.skeleton-title {
    height: 16px;
    width: 200px;
    border-radius: 8px;
}

.skeleton-badge {
    height: 20px;
    width: 60px;
    border-radius: 10px;
}

.skeleton-message {
    margin-bottom: 12px;
}

.skeleton-line {
    height: 14px;
    margin-bottom: 6px;
    border-radius: 7px;
}

.skeleton-line:last-child {
    margin-bottom: 0;
}

.skeleton-line.short {
    width: 60%;
}

.skeleton-line.medium {
    width: 75%;
}

.skeleton-meta {
    display: flex;
    align-items: center;
    gap: 12px;
}

.skeleton-time {
    height: 12px;
    width: 80px;
    border-radius: 6px;
}

.skeleton-category {
    height: 16px;
    width: 100px;
    border-radius: 8px;
}

.skeleton-actions {
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex-shrink: 0;
}

.skeleton-action-btn {
    height: 28px;
    width: 65px;
    border-radius: 4px;
}

.skeleton-action-btn.small {
    height: 24px;
    width: 50px;
}

/* Notification Detail Skeleton */
.notification-detail-skeleton {
    background: white;
    border-radius: 16px;
    padding: 32px;
    border: 2px solid #f0f0f0;
}

.skeleton-header-large {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.skeleton-icon-large {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    flex-shrink: 0;
}

.skeleton-title-large {
    height: 24px;
    flex: 1;
    border-radius: 12px;
}

.skeleton-badge-large {
    height: 28px;
    width: 80px;
    border-radius: 14px;
}

.skeleton-content-area {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.skeleton-paragraph {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.skeleton-details-box {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.skeleton-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.skeleton-label {
    height: 14px;
    width: 120px;
    border-radius: 7px;
}

.skeleton-value {
    height: 16px;
    width: 150px;
    border-radius: 8px;
}

.skeleton-value.long {
    width: 200px;
}

.skeleton-action-area {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.skeleton-primary-btn {
    height: 44px;
    width: 140px;
    border-radius: 22px;
}

.skeleton-secondary-btn {
    height: 44px;
    width: 120px;
    border-radius: 22px;
}

/* Dashboard Cards Skeleton */
.dashboard-skeleton-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.dashboard-card-skeleton {
    background: white;
    border-radius: 16px;
    padding: 24px;
    border: 2px solid #f0f0f0;
    animation: cardSlideUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

@keyframes cardSlideUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.skeleton-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.skeleton-card-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.skeleton-card-title {
    height: 16px;
    flex: 1;
    border-radius: 8px;
}

.skeleton-card-content {
    margin-bottom: 16px;
    text-align: center;
}

.skeleton-number {
    height: 32px;
    width: 80px;
    margin: 0 auto 8px;
    border-radius: 16px;
}

.skeleton-label-small {
    height: 12px;
    width: 60px;
    margin: 0 auto;
    border-radius: 6px;
}

.skeleton-card-footer {
    border-top: 1px solid #f0f0f0;
    padding-top: 12px;
}

.skeleton-trend {
    height: 14px;
    width: 120px;
    border-radius: 7px;
}

/* Review Notifications Skeleton */
.review-skeleton-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.review-skeleton-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #f0f0f0;
    animation: reviewSlideIn 0.6s ease-out forwards;
    opacity: 0;
    transform: translateX(-20px);
}

@keyframes reviewSlideIn {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.skeleton-review-header {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.skeleton-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    flex-shrink: 0;
}

.skeleton-review-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.skeleton-customer-name {
    height: 16px;
    width: 140px;
    border-radius: 8px;
}

.skeleton-venue-name {
    height: 14px;
    width: 180px;
    border-radius: 7px;
}

.skeleton-stars {
    display: flex;
    gap: 4px;
}

.skeleton-star {
    width: 16px;
    height: 16px;
    border-radius: 2px;
}

.skeleton-review-content {
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.skeleton-review-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
    .notification-skeleton-content {
        flex-direction: column;
        gap: 12px;
    }
    
    .skeleton-actions {
        flex-direction: row;
        justify-content: center;
    }
    
    .skeleton-header-large {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
    
    .skeleton-detail-row {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
    
    .skeleton-action-area {
        flex-direction: column;
        align-items: center;
    }
    
    .dashboard-skeleton-grid {
        grid-template-columns: 1fr;
    }
    
    .skeleton-review-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}

/* Loading indicators */
.skeleton-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, #3b82f6, transparent);
    animation: progressBar 2s infinite;
    z-index: 9999;
}

@keyframes progressBar {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Pulse animation for important elements */
.skeleton-icon,
.skeleton-icon-large,
.skeleton-avatar {
    animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Staggered animation delays for natural loading feeling */
.notification-skeleton-item:nth-child(1) { animation-delay: 0.1s; }
.notification-skeleton-item:nth-child(2) { animation-delay: 0.2s; }
.notification-skeleton-item:nth-child(3) { animation-delay: 0.3s; }
.notification-skeleton-item:nth-child(4) { animation-delay: 0.4s; }
.notification-skeleton-item:nth-child(5) { animation-delay: 0.5s; }
.notification-skeleton-item:nth-child(6) { animation-delay: 0.6s; }

.dashboard-card-skeleton:nth-child(1) { animation-delay: 0.1s; }
.dashboard-card-skeleton:nth-child(2) { animation-delay: 0.25s; }
.dashboard-card-skeleton:nth-child(3) { animation-delay: 0.4s; }
.dashboard-card-skeleton:nth-child(4) { animation-delay: 0.55s; }

.review-skeleton-item:nth-child(1) { animation-delay: 0.15s; }
.review-skeleton-item:nth-child(2) { animation-delay: 0.3s; }
.review-skeleton-item:nth-child(3) { animation-delay: 0.45s; }
</style>

<script>
// Auto-hide skeleton after timeout (fallback)
document.addEventListener('DOMContentLoaded', function() {
    const skeletons = document.querySelectorAll('.skeleton-container');
    
    skeletons.forEach(skeleton => {
        // Auto-hide after 10 seconds as fallback
        setTimeout(() => {
            if (skeleton.parentNode) {
                skeleton.style.transition = 'opacity 0.5s ease-out';
                skeleton.style.opacity = '0';
                setTimeout(() => {
                    if (skeleton.parentNode) {
                        skeleton.remove();
                    }
                }, 500);
            }
        }, 10000);
    });
});

// Helper function to show/hide skeleton
window.toggleSkeleton = function(containerId, show = true) {
    const container = document.getElementById(containerId);
    const skeleton = container?.querySelector('.skeleton-container');
    
    if (show && !skeleton) {
        // Create and show skeleton
        const skeletonHtml = document.querySelector('[data-skeleton-template]')?.innerHTML;
        if (skeletonHtml) {
            container.innerHTML = skeletonHtml;
        }
    } else if (!show && skeleton) {
        // Hide skeleton with animation
        skeleton.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
        skeleton.style.opacity = '0';
        skeleton.style.transform = 'translateY(-10px)';
        setTimeout(() => skeleton.remove(), 300);
    }
};

// Preload optimization
window.preloadSkeletons = function() {
    const skeletonTypes = ['notifications', 'notification_detail', 'dashboard_cards', 'review_notifications'];
    skeletonTypes.forEach(type => {
        const template = document.createElement('div');
        template.style.display = 'none';
        template.setAttribute('data-skeleton-type', type);
        template.innerHTML = '<!-- Skeleton content for ' + type + ' -->';
        document.body.appendChild(template);
    });
};
</script> 