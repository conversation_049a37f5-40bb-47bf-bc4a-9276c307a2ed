{% load static %}

<!-- Enhanced Review Notification Badge Component -->
<div class="review-notification-badge" data-notification-type="{{ notification.notification_type }}" data-review-id="{{ review.id|default:'' }}">
    <!-- Main Badge -->
    <div class="notification-badge-main">
        {% if notification.notification_type == 'review' %}
            <div class="badge-icon review-icon">
                <i class="fas fa-star"></i>
                {% if notification.related_object_type == 'ReviewResponse' %}
                    <div class="response-indicator">
                        <i class="fas fa-reply"></i>
                    </div>
                {% endif %}
            </div>
        {% endif %}
        
        <div class="badge-content">
            <div class="badge-title">
                {% if notification.related_object_type == 'ReviewResponse' %}
                    💬 Provider Response
                {% elif notification.related_object_type == 'Review' %}
                    ⭐ New Review
                {% else %}
                    {{ notification.title }}
                {% endif %}
            </div>
            
            <div class="badge-details">
                {% if notification.related_object_type == 'ReviewResponse' %}
                    {% if review %}
                        <div class="response-details">
                            <div class="venue-name">{{ review.venue.venue_name }}</div>
                            <div class="provider-name">{{ review.venue.service_provider.business_name }}</div>
                            <div class="original-rating">
                                <span class="stars">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.rating %}★{% else %}☆{% endif %}
                                    {% endfor %}
                                </span>
                                <span class="rating-text">{{ review.rating }}/5</span>
                            </div>
                        </div>
                    {% endif %}
                {% elif notification.related_object_type == 'Review' %}
                    {% if review %}
                        <div class="review-details">
                            <div class="customer-name">{{ review.customer.get_full_name|default:review.customer.email }}</div>
                            <div class="venue-name">{{ review.venue.venue_name }}</div>
                            <div class="review-rating">
                                <span class="stars">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.rating %}★{% else %}☆{% endif %}
                                    {% endfor %}
                                </span>
                                <span class="rating-text">{{ review.rating }}/5</span>
                            </div>
                        </div>
                    {% endif %}
                {% endif %}
            </div>
            
            <div class="badge-meta">
                <span class="time-ago">{{ notification.created_at|timesince }} ago</span>
                {% if notification.read_status == 'unread' %}
                    <span class="unread-indicator">NEW</span>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="badge-actions">
        {% if notification.related_object_type == 'ReviewResponse' %}
            <a href="{% url 'review_app:customer_review_detail' review.id %}" class="action-btn primary" title="View Conversation">
                <i class="fas fa-eye"></i>
            </a>
            <a href="{% url 'venues_app:venue_detail' review.venue.slug %}" class="action-btn secondary" title="Visit Venue">
                <i class="fas fa-external-link-alt"></i>
            </a>
        {% elif notification.related_object_type == 'Review' %}
            <a href="{% url 'review_app:provider_review_detail' review.id %}" class="action-btn primary" title="View Review">
                <i class="fas fa-eye"></i>
            </a>
            {% if not review.response %}
                <a href="{% url 'review_app:provider_respond_to_review' review.id %}" class="action-btn success" title="Respond">
                    <i class="fas fa-reply"></i>
                </a>
            {% endif %}
        {% endif %}
        
        <!-- Mark as read/unread -->
        <form method="post" action="{% if notification.read_status == 'unread' %}{% url 'notifications_app:mark_notification_read' notification.id %}{% else %}{% url 'notifications_app:mark_notification_unread' notification.id %}{% endif %}" class="d-inline">
            {% csrf_token %}
            <button type="submit" class="action-btn {% if notification.read_status == 'unread' %}info{% else %}outline{% endif %}" title="{% if notification.read_status == 'unread' %}Mark as Read{% else %}Mark as Unread{% endif %}">
                <i class="fas {% if notification.read_status == 'unread' %}fa-envelope-open{% else %}fa-envelope{% endif %}"></i>
            </button>
        </form>
    </div>
</div>

<style>
.review-notification-badge {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.review-notification-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.review-notification-badge[data-notification-type="review"]:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.notification-badge-main {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
}

.badge-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    position: relative;
    flex-shrink: 0;
}

.badge-icon.review-icon {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

.response-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: white;
    border: 2px solid white;
}

.badge-content {
    flex: 1;
    min-width: 0;
}

.badge-title {
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.badge-details {
    margin-bottom: 8px;
}

.response-details, .review-details {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    border-left: 3px solid #007bff;
}

.venue-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    margin-bottom: 4px;
}

.provider-name, .customer-name {
    color: #6c757d;
    font-size: 13px;
    margin-bottom: 6px;
}

.original-rating, .review-rating {
    display: flex;
    align-items: center;
    gap: 6px;
}

.stars {
    color: #ffc107;
    font-size: 14px;
}

.rating-text {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.badge-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
}

.time-ago {
    color: #6c757d;
    font-size: 12px;
}

.unread-indicator {
    background: #dc3545;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: flex-end;
    padding-top: 8px;
    border-top: 1px solid #e9ecef;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.action-btn.primary {
    background: #007bff;
    color: white;
}

.action-btn.primary:hover {
    background: #0056b3;
    color: white;
}

.action-btn.secondary {
    background: #6c757d;
    color: white;
}

.action-btn.secondary:hover {
    background: #495057;
    color: white;
}

.action-btn.success {
    background: #28a745;
    color: white;
}

.action-btn.success:hover {
    background: #1e7e34;
    color: white;
}

.action-btn.info {
    background: #17a2b8;
    color: white;
}

.action-btn.info:hover {
    background: #138496;
    color: white;
}

.action-btn.outline {
    background: transparent;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.action-btn.outline:hover {
    background: #f8f9fa;
    color: #495057;
}

/* Responsive Design */
@media (max-width: 576px) {
    .review-notification-badge {
        padding: 12px;
    }
    
    .notification-badge-main {
        gap: 8px;
    }
    
    .badge-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .badge-title {
        font-size: 14px;
    }
    
    .venue-name {
        font-size: 13px;
    }
    
    .response-details, .review-details {
        padding: 8px;
    }
    
    .badge-actions {
        gap: 6px;
    }
    
    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 11px;
    }
}
</style> 